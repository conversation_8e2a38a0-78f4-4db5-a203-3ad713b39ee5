# 辅助计量单位配置化管理实现总结

## 概述
根据用户需求，实现了辅助计量单位的配置化管理。ProductionPackagingSpecification字段仅包含重量值（如'25KG'），auxiliaryMeasurementUnit字段仅包含单位类型（如'袋'、'包'），最终显示格式为'{weight}/{unit}'如'25KG/袋'。

## 实现内容

### 1. 实体类修改

#### OutboundInformationTransferRecord实体类
- **文件位置**: `src/main/java/com/haihang/model/DO/outbound/OutboundInformationTransferRecord.java`
- **修改内容**: 添加了`productionPackagingSpecification`字段，映射到数据库字段`gg_xh`
- **字段说明**: 
  - `productionPackagingSpecification`: 产品规格（包装规格），仅存储重量值，如"25KG"、"50KG"
  - `auxiliaryMeasurementUnit`: 辅助计量单位，已存在，映射到数据库字段`bz_xs`

#### TransferRecordDTO类
- **文件位置**: `src/main/java/com/haihang/model/DTO/outbound/transferRecord/TransferRecordDTO.java`
- **修改内容**: 添加了`auxiliaryMeasurementUnit`字段

### 2. 新建配置实体类和相关组件

#### AuxiliaryMeasurementUnitConfig实体类
- **文件位置**: `src/main/java/com/haihang/model/DO/outbound/AuxiliaryMeasurementUnitConfig.java`
- **字段说明**:
  - `id`: 主键ID（自增）
  - `groupCode`: 集团编号（String，可为空，对应Group实体的groupNumber字段）
  - `groupId`: 集团ID（Long，可为空，对应TransportReportGroup实体的id字段）
  - `customerCode`: 客户编号（String，可为空）
  - `productCategoryCode`: 产品类别编号（String，可为空）
  - `productCategoryName`: 产品类别名称（String，可为空）
  - `productCode`: 产品编号（String，可为空）
  - `productName`: 产品名称（String，可为空）
  - `auxiliaryMeasurementUnit`: 辅助计量单位（String，非空，如"袋"、"包"、"箱"）
  - `createTime`: 创建时间
  - `updateTime`: 更新时间

#### AuxiliaryMeasurementUnitConfigMapper接口
- **文件位置**: `src/main/java/com/haihang/mapper/outbound/AuxiliaryMeasurementUnitConfigMapper.java`
- **继承**: `MPJBaseMapper<AuxiliaryMeasurementUnitConfig>`

#### AuxiliaryMeasurementUnitConfigService接口
- **文件位置**: `src/main/java/com/haihang/service/outbound/AuxiliaryMeasurementUnitConfigService.java`
- **核心方法**: `findAuxiliaryMeasurementUnit()` - 根据优先级查找辅助计量单位配置

#### AuxiliaryMeasurementUnitConfigServiceImpl实现类
- **文件位置**: `src/main/java/com/haihang/service/outbound/impl/AuxiliaryMeasurementUnitConfigServiceImpl.java`
- **实现逻辑**: 按优先级查找配置的完整实现

### 3. 优先级查询规则实现

实现了基于优先级的查找逻辑（按优先级从高到低）：
1. **客户编号 + 产品编号**（最高优先级）
2. **客户编号 + 产品类别编号**
3. **集团编号/集团ID + 产品编号**
4. **集团编号/集团ID + 产品类别编号**（最低优先级）

### 4. 业务逻辑修改

#### TransferRecordServiceImpl类修改
- **文件位置**: `src/main/java/com/haihang/service/outbound/impl/TransferRecordServiceImpl.java`
- **修改内容**:
  1. 添加了`AuxiliaryMeasurementUnitConfigService`的注入
  2. 新增了`getAuxiliaryMeasurementUnit()`私有方法，实现辅助计量单位的获取逻辑
  3. 修改了第867-881行的包装规格处理逻辑，替换原有的默认逻辑
  4. 在第478-488行添加了辅助计量单位的设置逻辑

#### auxiliaryMeasurementUnit字段赋值逻辑
按优先级执行以下逻辑：
1. **首先查询AuxiliaryMeasurementUnitConfig配置表**，根据客户信息和产品信息按优先级规则匹配配置
2. **如果配置表无匹配记录，且当前记录的auxiliaryMeasurementUnit字段已有值**，则保留该字段的现有值
3. **如果以上条件都不满足，则使用默认逻辑**：根据productionPackagingSpecification中的数值，>100时设置为"包"，否则设置为"袋"

### 5. 数据库表结构

#### auxiliary_measurement_unit_config表
- **文件位置**: `auxiliary_measurement_unit_config_table.sql`
- **表结构**: 包含所有必要字段和优化查询性能的复合索引
- **索引设计**:
  - `idx_customer_product`: (customer_code, product_code) - 用于优先级1查询
  - `idx_customer_category`: (customer_code, product_category_code) - 用于优先级2查询
  - `idx_group_product`: (group_code, group_id, product_code) - 用于优先级3查询
  - `idx_group_category`: (group_code, group_id, product_category_code) - 用于优先级4查询

## 字段区分说明

- **productionPackagingSpecification**: 仅存储重量规格（如"25KG"、"50KG"）
- **auxiliaryMeasurementUnit**: 仅存储计量单位类型（如"袋"、"包"、"箱"）
- **最终显示**: 组合为完整格式：productionPackagingSpecification + "/" + auxiliaryMeasurementUnit

## 使用示例

```java
// 获取辅助计量单位
String auxiliaryUnit = auxiliaryMeasurementUnitConfigService.findAuxiliaryMeasurementUnit(
    "CUSTOMER001",     // 客户编号
    "PRODUCT001",      // 产品编号
    "CATEGORY001",     // 产品类别编号
    "GROUP001",        // 集团编号
    1L                 // 集团ID
);

// 最终显示格式
String finalDisplay = "25KG" + "/" + auxiliaryUnit; // 例如: "25KG/袋"
```

## 部署说明

1. **执行SQL脚本**: 运行`auxiliary_measurement_unit_config_table.sql`创建数据库表
2. **重启应用**: 确保新的Service和Mapper被正确注入
3. **配置数据**: 根据实际业务需求在`auxiliary_measurement_unit_config`表中添加配置数据

## 注意事项

1. 配置表支持灵活的配置组合，可以只配置部分字段
2. 优先级查询确保了最精确的匹配优先返回
3. 保持了向后兼容性，现有数据不会受到影响
4. 默认逻辑作为兜底方案，确保系统稳定运行
