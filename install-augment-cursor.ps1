# Augment Extension Auto-Install Script - PowerShell Version
# For Cursor Editor on Windows Systems
# Author: AI Assistant
# Version: 1.0

param(
    [string]$Version = "latest",
    [switch]$Force,
    [switch]$Verbose
)

# Set error handling
$ErrorActionPreference = "Stop"

# Configuration
$PUBLISHER = "augment"
$EXTENSION_NAME = "vscode-augment"
$EXTENSION_ID = "$PUBLISHER.$EXTENSION_NAME"
$DOWNLOAD_DIR = "$env:TEMP\augment-install"
$CURSOR_PATHS = @(
    "$env:LOCALAPPDATA\Programs\cursor\bin\cursor.cmd",
    "$env:PROGRAMFILES\Cursor\bin\cursor.cmd",
    "cursor"
)

# Color output functions
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# Check if Cursor is installed
function Test-CursorInstallation {
    Write-Info "Checking Cursor editor installation..."

    foreach ($path in $CURSOR_PATHS) {
        try {
            $result = & $path --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Found Cursor: $path"
                Write-Info "Version info: $result"
                return $path
            }
        }
        catch {
            continue
        }
    }

    throw "Cursor editor not found. Please ensure Cursor is properly installed and added to PATH."
}

# Get latest extension version info
function Get-LatestVersion {
    Write-Info "Getting latest Augment extension version..."
    
    try {
        $apiUrl = "https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery"
        $body = @{
            filters = @(
                @{
                    criteria = @(
                        @{
                            filterType = 7
                            value = $EXTENSION_ID
                        }
                    )
                }
            )
            flags = 914
        } | ConvertTo-Json -Depth 10
        
        $headers = @{
            "Content-Type" = "application/json"
            "Accept" = "application/json;api-version=3.0-preview.1"
        }
        
        $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Body $body -Headers $headers
        $extension = $response.results[0].extensions[0]
        $latestVersion = $extension.versions[0].version
        
        Write-Success "Latest version: $latestVersion"
        return $latestVersion
    }
    catch {
        Write-Warning "Unable to get latest version info, using 'latest' identifier"
        return "latest"
    }
}

# Build download URLs
function Get-DownloadUrls {
    param([string]$Version)

    $urls = @()

    # New format URL (recommended)
    if ($Version -ne "latest") {
        $urls += "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/$PUBLISHER/vsextensions/$EXTENSION_NAME/$Version/vspackage"
    }

    # Old format URL (backup)
    $urls += "https://$PUBLISHER.gallery.vsassets.io/_apis/public/gallery/publisher/$PUBLISHER/extension/$EXTENSION_NAME/$Version/assetbyname/Microsoft.VisualStudio.Services.VSIXPackage"

    # Generic latest version URL
    $urls += "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/$PUBLISHER/vsextensions/$EXTENSION_NAME/latest/vspackage"

    return $urls
}

# Download VSIX file
function Get-VSIXFile {
    param([string]$Version)

    # Create download directory
    if (!(Test-Path $DOWNLOAD_DIR)) {
        New-Item -ItemType Directory -Path $DOWNLOAD_DIR -Force | Out-Null
    }

    $downloadUrls = Get-DownloadUrls -Version $Version
    $fileName = "$EXTENSION_NAME-$Version.vsix"
    $filePath = Join-Path $DOWNLOAD_DIR $fileName

    # If file exists and not forcing re-download
    if ((Test-Path $filePath) -and !$Force) {
        Write-Info "VSIX file already exists: $filePath"
        return $filePath
    }

    Write-Info "Starting Augment extension download..."

    foreach ($url in $downloadUrls) {
        try {
            Write-Info "Trying download: $url"

            # Use Invoke-WebRequest to download
            $progressPreference = $ProgressPreference
            $ProgressPreference = 'SilentlyContinue'

            Invoke-WebRequest -Uri $url -OutFile $filePath -UseBasicParsing

            $ProgressPreference = $progressPreference

            # Verify downloaded file
            if ((Test-Path $filePath) -and (Get-Item $filePath).Length -gt 1KB) {
                $fileSize = [math]::Round((Get-Item $filePath).Length / 1MB, 2)
                Write-Success "Download successful! File size: ${fileSize}MB"
                Write-Success "Saved to: $filePath"
                return $filePath
            }
            else {
                Remove-Item $filePath -ErrorAction SilentlyContinue
            }
        }
        catch {
            Write-Warning "Download failed: $($_.Exception.Message)"
            continue
        }
    }

    throw "All download URLs failed. Please check network connection or try again later."
}

# Install VSIX to Cursor
function Install-VSIXToCursor {
    param(
        [string]$VSIXPath,
        [string]$CursorPath
    )

    Write-Info "Starting Augment extension installation to Cursor..."

    try {
        # Execute install command
        $installArgs = @("--install-extension", $VSIXPath)

        Write-Info "Executing command: $CursorPath $($installArgs -join ' ')"

        $process = Start-Process -FilePath $CursorPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow

        if ($process.ExitCode -eq 0) {
            Write-Success "Augment extension installed successfully!"
            return $true
        }
        else {
            Write-Error "Installation failed with exit code: $($process.ExitCode)"
            return $false
        }
    }
    catch {
        Write-Error "Error during installation: $($_.Exception.Message)"
        return $false
    }
}

# Verify installation
function Test-Installation {
    param([string]$CursorPath)

    Write-Info "Verifying installation..."

    try {
        $result = & $CursorPath --list-extensions 2>$null
        if ($result -contains $EXTENSION_ID) {
            Write-Success "Verification successful! Augment extension is properly installed."
            return $true
        }
        else {
            Write-Warning "Verification failed: Augment not found in installed extensions list."
            return $false
        }
    }
    catch {
        Write-Warning "Unable to verify installation status: $($_.Exception.Message)"
        return $false
    }
}

# Clean up temporary files
function Clear-TempFiles {
    param([string]$KeepVSIX)

    if (!$KeepVSIX -and (Test-Path $DOWNLOAD_DIR)) {
        try {
            Remove-Item $DOWNLOAD_DIR -Recurse -Force
            Write-Info "Temporary files cleaned up"
        }
        catch {
            Write-Warning "Failed to clean up temporary files: $($_.Exception.Message)"
        }
    }
}

# Main function
function Main {
    Write-Info "=== Augment Extension Auto-Install Script ==="
    Write-Info "Target Editor: Cursor"
    Write-Info "Extension ID: $EXTENSION_ID"
    Write-Info "Requested Version: $Version"
    Write-Info ""

    try {
        # 1. Check Cursor installation
        $cursorPath = Test-CursorInstallation

        # 2. Get version info
        if ($Version -eq "latest") {
            $actualVersion = Get-LatestVersion
        }
        else {
            $actualVersion = $Version
        }

        # 3. Download VSIX file
        $vsixPath = Get-VSIXFile -Version $actualVersion

        # 4. Install extension
        $installSuccess = Install-VSIXToCursor -VSIXPath $vsixPath -CursorPath $cursorPath

        if ($installSuccess) {
            # 5. Verify installation
            $verifySuccess = Test-Installation -CursorPath $cursorPath

            if ($verifySuccess) {
                Write-Success ""
                Write-Success "🎉 Installation Complete!"
                Write-Success "Please restart Cursor editor to ensure the extension works properly."
                Write-Success "Then use Ctrl+L or Cmd+L to open the Augment panel."
            }
        }

        # 6. Clean up temporary files (keep VSIX file for future use)
        Clear-TempFiles -KeepVSIX $true

    }
    catch {
        Write-Error ""
        Write-Error "❌ Installation Failed: $($_.Exception.Message)"
        Write-Error ""
        Write-Error "Troubleshooting suggestions:"
        Write-Error "1. Ensure Cursor editor is properly installed"
        Write-Error "2. Check network connection"
        Write-Error "3. Try running this script as administrator"
        Write-Error "4. Use -Verbose parameter for detailed information"

        exit 1
    }
}

# Show help information
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host @"
Augment Extension Auto-Install Script - PowerShell Version

Usage:
    .\install-augment-cursor.ps1 [options]

Options:
    -Version <version>  Specify version to install (default: latest)
    -Force              Force re-download even if file exists
    -Verbose            Show detailed output information
    -h, --help          Show this help information

Examples:
    .\install-augment-cursor.ps1                    # Install latest version
    .\install-augment-cursor.ps1 -Version 0.487.0  # Install specific version
    .\install-augment-cursor.ps1 -Force             # Force re-download and install

"@
    exit 0
}

# Execute main function
Main
